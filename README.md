# <PERSON><PERSON> thống <PERSON>u<PERSON> l<PERSON>, <PERSON>ư<PERSON>i Dùng và <PERSON> (QLS)

## <PERSON><PERSON> tả
Đây là một hệ thống quản lý hoàn chỉnh với:
- **Frontend**: Giao diện web sử dụng HTML, CSS, JavaScript và Bootstrap
- **Backend**: API RESTful sử dụng .NET Core 9.0 với Entity Framework Core
- **Database**: In-Memory Database (c<PERSON> thể chuyển sang SQL Server)
- **API Documentation**: Swagger UI

## Cấu trúc dự án
```
QLS/
├── index.html          # Giao diện chính
├── main.js            # Logic JavaScript
├── style.css          # CSS tùy chỉnh
└── backend/
    └── QLS.API/       # .NET Core Web API
        ├── Controllers/
        ├── Models/
        ├── Data/
        └── Program.cs
```

## Chức năng
### 1. <PERSON>u<PERSON><PERSON> lý <PERSON> (Products)
- <PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>, x<PERSON><PERSON> sách
- <PERSON><PERSON> danh mục cho sách
- <PERSON><PERSON><PERSON> thị danh sách sách với thông tin danh mục

### 2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> (Users)
- <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, x<PERSON><PERSON> ngườ<PERSON> dùng
- <PERSON>uản lý thông tin: tên, email, mật khẩu, vai trò, ngày sinh, địa chỉ, số điện thoại

### 3. Quản lý Danh Mục (Categories)
- Thêm, sửa, xóa danh mục
- Mỗi danh mục có tên và mô tả

## Cách chạy ứng dụng

### 1. Chạy Backend API
```bash
cd backend/QLS.API
dotnet run
```
API sẽ chạy tại: `http://localhost:5184`

### 2. Truy cập Frontend
Mở file `index.html` trong trình duyệt hoặc truy cập:
`file:///c:/Users/<USER>/Downloads/QLS/index.html`

### 3. Truy cập Swagger UI
Mở trình duyệt và truy cập:
`http://localhost:5184/swagger`

## API Endpoints

### Products (Sách)
- `GET /api/products` - Lấy danh sách tất cả sách
- `GET /api/products/{id}` - Lấy thông tin sách theo ID
- `POST /api/products` - Thêm sách mới
- `PUT /api/products/{id}` - Cập nhật sách
- `DELETE /api/products/{id}` - Xóa sách

### Users (Người dùng)
- `GET /api/users` - Lấy danh sách tất cả người dùng
- `GET /api/users/{id}` - Lấy thông tin người dùng theo ID
- `POST /api/users` - Thêm người dùng mới
- `PUT /api/users/{id}` - Cập nhật người dùng
- `DELETE /api/users/{id}` - Xóa người dùng

### Categories (Danh mục)
- `GET /api/categories` - Lấy danh sách tất cả danh mục
- `GET /api/categories/{id}` - Lấy thông tin danh mục theo ID
- `POST /api/categories` - Thêm danh mục mới
- `PUT /api/categories/{id}` - Cập nhật danh mục
- `DELETE /api/categories/{id}` - Xóa danh mục

## Dữ liệu mẫu
Hệ thống đã được cấu hình với dữ liệu mẫu:

### Danh mục:
1. Văn học - Sách văn học trong và ngoài nước
2. Khoa học - Sách khoa học và công nghệ
3. Giáo dục - Sách giáo khoa và tham khảo

### Sách:
1. Tắt đèn - 50,000 VND (Danh mục: Văn học)
2. Lập trình C# - 120,000 VND (Danh mục: Khoa học)

### Người dùng:
1. Admin - <EMAIL> (Vai trò: Administrator)

## Công nghệ sử dụng
- **Frontend**: HTML5, CSS3, JavaScript ES6, Bootstrap 5.3
- **Backend**: .NET Core 9.0, ASP.NET Core Web API
- **Database**: Entity Framework Core với In-Memory Database
- **API Documentation**: Swagger/OpenAPI 3.0
- **CORS**: Đã được cấu hình để frontend có thể gọi API

## Tính năng nổi bật
- ✅ Giao diện responsive với Bootstrap
- ✅ API RESTful hoàn chỉnh với CRUD operations
- ✅ Swagger UI để test API
- ✅ Xử lý lỗi và validation
- ✅ CORS được cấu hình
- ✅ Dữ liệu mẫu sẵn có
- ✅ Quan hệ giữa các bảng (Product-Category)
