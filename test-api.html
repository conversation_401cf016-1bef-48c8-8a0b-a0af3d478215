<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLS API Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .test-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">🧪 QLS API Test Suite</h1>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API Endpoints Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testAllEndpoints()">Test All Endpoints</button>
                        <button class="btn btn-success mb-2" onclick="testCategories()">Test Categories</button>
                        <button class="btn btn-info mb-2" onclick="testProducts()">Test Products</button>
                        <button class="btn btn-warning mb-2" onclick="testUsers()">Test Users</button>
                        <button class="btn btn-danger mb-2" onclick="clearResults()">Clear Results</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>CRUD Operations Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary mb-2" onclick="testCreateCategory()">Create Category</button>
                        <button class="btn btn-outline-success mb-2" onclick="testCreateProduct()">Create Product</button>
                        <button class="btn btn-outline-info mb-2" onclick="testCreateUser()">Create User</button>
                        <button class="btn btn-outline-secondary mb-2" onclick="testFullCRUD()">Full CRUD Test</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5184/api';
        
        function addResult(message, type = 'info', data = null) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            
            let content = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            if (data) {
                content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            }
            
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }
        
        async function testEndpoint(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.text();
                
                if (response.ok) {
                    addResult(`✅ ${method} ${url} - Success (${response.status})`, 'success', 
                             result ? JSON.parse(result) : null);
                    return JSON.parse(result || '{}');
                } else {
                    addResult(`❌ ${method} ${url} - Error (${response.status})`, 'error', result);
                    return null;
                }
            } catch (error) {
                addResult(`❌ ${method} ${url} - Network Error: ${error.message}`, 'error');
                return null;
            }
        }
        
        async function testCategories() {
            addResult('🧪 Testing Categories API...', 'info');
            await testEndpoint(`${API_BASE}/categories`);
        }
        
        async function testProducts() {
            addResult('🧪 Testing Products API...', 'info');
            await testEndpoint(`${API_BASE}/products`);
        }
        
        async function testUsers() {
            addResult('🧪 Testing Users API...', 'info');
            await testEndpoint(`${API_BASE}/users`);
        }
        
        async function testAllEndpoints() {
            addResult('🚀 Starting comprehensive API test...', 'info');
            await testCategories();
            await testProducts();
            await testUsers();
            addResult('✅ All endpoint tests completed!', 'success');
        }
        
        async function testCreateCategory() {
            const newCategory = {
                name: `Test Category ${Date.now()}`,
                description: 'This is a test category created by API test'
            };
            
            addResult('🧪 Testing Create Category...', 'info');
            const result = await testEndpoint(`${API_BASE}/categories`, 'POST', newCategory);
            
            if (result) {
                addResult(`✅ Category created with ID: ${result.id}`, 'success');
            }
        }
        
        async function testCreateProduct() {
            const newProduct = {
                name: `Test Product ${Date.now()}`,
                price: 99.99,
                description: 'This is a test product created by API test',
                categoryId: 1
            };
            
            addResult('🧪 Testing Create Product...', 'info');
            const result = await testEndpoint(`${API_BASE}/products`, 'POST', newProduct);
            
            if (result) {
                addResult(`✅ Product created with ID: ${result.id}`, 'success');
            }
        }
        
        async function testCreateUser() {
            const newUser = {
                name: `Test User ${Date.now()}`,
                email: `test${Date.now()}@example.com`,
                password: 'testpassword123',
                role: 'User'
            };
            
            addResult('🧪 Testing Create User...', 'info');
            const result = await testEndpoint(`${API_BASE}/users`, 'POST', newUser);
            
            if (result) {
                addResult(`✅ User created with ID: ${result.id}`, 'success');
            }
        }
        
        async function testFullCRUD() {
            addResult('🚀 Starting Full CRUD Test...', 'info');
            
            // Test Create
            await testCreateCategory();
            await testCreateProduct();
            await testCreateUser();
            
            // Test Read
            await testAllEndpoints();
            
            addResult('✅ Full CRUD test completed!', 'success');
        }
        
        // Auto-run basic test on page load
        window.onload = function() {
            addResult('🎯 QLS API Test Suite initialized', 'info');
            addResult('Click "Test All Endpoints" to start testing', 'info');
        };
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
