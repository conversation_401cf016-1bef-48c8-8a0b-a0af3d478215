<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QLS - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .dashboard-container {
            padding: 50px 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
        }
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn-custom:hover {
            transform: scale(1.05);
            text-decoration: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✅";
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container dashboard-container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="text-center mb-5">
                    <h1 class="text-white mb-3">🏢 Hệ thống Quản lý QLS</h1>
                    <p class="text-white-50 lead">Quản lý Sách, Người Dùng và Danh Mục</p>
                </div>

                <div class="row">
                    <!-- Frontend Card -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">🖥️ Giao diện Người dùng</h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Giao diện web để quản lý sách, người dùng và danh mục với Bootstrap UI.</p>
                                <ul class="feature-list">
                                    <li>Quản lý Sách</li>
                                    <li>Quản lý Người dùng</li>
                                    <li>Quản lý Danh mục</li>
                                    <li>Giao diện responsive</li>
                                </ul>
                                <div class="text-center mt-3">
                                    <a href="index.html" class="btn btn-primary btn-custom" target="_blank">
                                        Mở Ứng dụng
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Documentation Card -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">📚 API Documentation</h5>
                            </div>
                            <div class="card-body">
                                <p class="card-text">Swagger UI để test và khám phá các API endpoints.</p>
                                <ul class="feature-list">
                                    <li>Products API</li>
                                    <li>Users API</li>
                                    <li>Categories API</li>
                                    <li>Interactive testing</li>
                                </ul>
                                <div class="text-center mt-3">
                                    <a href="http://localhost:5184/swagger" class="btn btn-success btn-custom" target="_blank">
                                        Mở Swagger UI
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Card -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">📊 Trạng thái Hệ thống</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Backend API</h6>
                                        <p>
                                            <span class="status-indicator status-online"></span>
                                            <strong>Online</strong> - http://localhost:5184
                                        </p>
                                        <small class="text-muted">
                                            API đang chạy và sẵn sàng nhận requests
                                        </small>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Database</h6>
                                        <p>
                                            <span class="status-indicator status-online"></span>
                                            <strong>Connected</strong> - In-Memory Database
                                        </p>
                                        <small class="text-muted">
                                            Dữ liệu mẫu đã được tải
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">🔗 Liên kết Nhanh</h5>
                            </div>
                            <div class="card-body text-center">
                                <a href="http://localhost:5184/api/products" class="btn btn-outline-primary btn-custom" target="_blank">
                                    API Products
                                </a>
                                <a href="http://localhost:5184/api/users" class="btn btn-outline-success btn-custom" target="_blank">
                                    API Users
                                </a>
                                <a href="http://localhost:5184/api/categories" class="btn btn-outline-info btn-custom" target="_blank">
                                    API Categories
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0">📋 Hướng dẫn Sử dụng</h5>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Đảm bảo API đang chạy:</strong> Mở terminal và chạy <code>dotnet run</code> trong thư mục <code>backend/QLS.API</code></li>
                                    <li><strong>Truy cập giao diện:</strong> Click vào "Mở Ứng dụng" để sử dụng giao diện quản lý</li>
                                    <li><strong>Test API:</strong> Click vào "Mở Swagger UI" để test các API endpoints</li>
                                    <li><strong>Xem dữ liệu:</strong> Sử dụng các liên kết nhanh để xem dữ liệu JSON trực tiếp</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
