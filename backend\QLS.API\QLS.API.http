@QLS.API_HostAddress = http://localhost:5184

GET {{QLS.API_HostAddress}}/weatherforecast/
Accept: application/json

###

# Authentication API Tests

### Đăng nhập với admin (có sẵn trong database)
POST {{QLS.API_HostAddress}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}

###

### Đăng nhập với thông tin sai
POST {{QLS.API_HostAddress}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}

###

### Đăng nhập với email không tồn tại
POST {{QLS.API_HostAddress}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

###

### Đăng ký người dùng mới
POST {{QLS.API_HostAddress}}/api/auth/register
Content-Type: application/json

{
  "name": "<PERSON><PERSON><PERSON> Van A",
  "email": "nguy<PERSON><PERSON>@example.com",
  "password": "password123",
  "role": "User",
  "phoneNumber": "0123456789",
  "address": "123 Đường ABC, TP.HCM"
}

###

### Kiểm tra email có tồn tại không
GET {{QLS.API_HostAddress}}/api/auth/check-email/<EMAIL>
Accept: application/json

###

### Lấy danh sách tất cả users
GET {{QLS.API_HostAddress}}/api/users
Accept: application/json

###
