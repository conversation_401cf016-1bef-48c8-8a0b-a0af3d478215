<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Đăng Nhập - QLS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .result-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #198754; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <h1 class="text-center mb-4">Test API Đăng Nhập QLS</h1>
                
                <!-- Form Đăng Nhập -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><PERSON><PERSON>ng <PERSON>p</h5>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label for="loginEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="loginEmail" value="<EMAIL>" required>
                            </div>
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">Mật khẩu</label>
                                <input type="password" class="form-control" id="loginPassword" value="admin123" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Đăng Nhập</button>
                        </form>
                        <div id="loginResult" class="result-box" style="display: none;"></div>
                    </div>
                </div>

                <!-- Form Đăng Ký -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Đăng Ký</h5>
                    </div>
                    <div class="card-body">
                        <form id="registerForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="registerName" class="form-label">Họ tên</label>
                                    <input type="text" class="form-control" id="registerName" value="Nguyen Van Test" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="registerEmail" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="registerEmail" value="<EMAIL>" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="registerPassword" class="form-label">Mật khẩu</label>
                                    <input type="password" class="form-control" id="registerPassword" value="password123" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="registerRole" class="form-label">Vai trò</label>
                                    <select class="form-control" id="registerRole">
                                        <option value="User">User</option>
                                        <option value="Administrator">Administrator</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="registerPhone" class="form-label">Số điện thoại</label>
                                    <input type="tel" class="form-control" id="registerPhone" value="0123456789">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="registerAddress" class="form-label">Địa chỉ</label>
                                    <input type="text" class="form-control" id="registerAddress" value="123 Đường ABC, TP.HCM">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-success">Đăng Ký</button>
                        </form>
                        <div id="registerResult" class="result-box" style="display: none;"></div>
                    </div>
                </div>

                <!-- Kiểm tra Email -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Kiểm tra Email</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="email" class="form-control" id="checkEmail" placeholder="Nhập email để kiểm tra" value="<EMAIL>">
                            <button class="btn btn-info" type="button" onclick="checkEmailExists()">Kiểm tra</button>
                        </div>
                        <div id="checkEmailResult" class="result-box" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5184/api';

        // Đăng nhập
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const result = await response.json();
                displayResult('loginResult', result, response.ok);
                
                if (result.success) {
                    localStorage.setItem('currentUser', JSON.stringify(result.user));
                }
            } catch (error) {
                displayResult('loginResult', { success: false, message: 'Lỗi kết nối: ' + error.message }, false);
            }
        });

        // Đăng ký
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const userData = {
                name: document.getElementById('registerName').value,
                email: document.getElementById('registerEmail').value,
                password: document.getElementById('registerPassword').value,
                role: document.getElementById('registerRole').value,
                phoneNumber: document.getElementById('registerPhone').value,
                address: document.getElementById('registerAddress').value
            };
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });
                
                const result = await response.json();
                displayResult('registerResult', result, response.ok);
            } catch (error) {
                displayResult('registerResult', { success: false, message: 'Lỗi kết nối: ' + error.message }, false);
            }
        });

        // Kiểm tra email
        async function checkEmailExists() {
            const email = document.getElementById('checkEmail').value;
            
            if (!email) {
                alert('Vui lòng nhập email');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}/auth/check-email/${encodeURIComponent(email)}`);
                const exists = await response.json();
                
                const result = {
                    success: true,
                    message: exists ? 'Email đã tồn tại trong hệ thống' : 'Email chưa được sử dụng',
                    data: { exists }
                };
                
                displayResult('checkEmailResult', result, true);
            } catch (error) {
                displayResult('checkEmailResult', { success: false, message: 'Lỗi kết nối: ' + error.message }, false);
            }
        }

        // Hiển thị kết quả
        function displayResult(elementId, result, isSuccess) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `
                <div class="${result.success ? 'success' : 'error'}">
                    <strong>${result.success ? '✅ Thành công' : '❌ Lỗi'}:</strong> ${result.message}
                </div>
                <pre class="mt-2">${JSON.stringify(result, null, 2)}</pre>
            `;
        }

        // Hiển thị thông tin user hiện tại nếu có
        window.addEventListener('load', function() {
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                console.log('User hiện tại:', JSON.parse(currentUser));
            }
        });
    </script>
</body>
</html>
