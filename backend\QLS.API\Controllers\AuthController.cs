using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using QLS.API.Data;
using QLS.API.Models;

namespace QLS.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly ApplicationDbContext _context;

        public AuthController(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Đăng nhập người dùng
        /// </summary>
        /// <param name="loginRequest">Thông tin đăng nhập</param>
        /// <returns>Kết quả đăng nhập</returns>
        [HttpPost("login")]
        public async Task<ActionResult<LoginResponse>> Login([FromBody] LoginRequest loginRequest)
        {
            try
            {
                // Kiểm tra dữ liệu đầu vào
                if (!ModelState.IsValid)
                {
                    return BadRequest(new LoginResponse
                    {
                        Success = false,
                        Message = "Dữ liệu đầu vào không hợp lệ"
                    });
                }

                // Tìm user theo email
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == loginRequest.Email.ToLower());

                if (user == null)
                {
                    return Ok(new LoginResponse
                    {
                        Success = false,
                        Message = "Email không tồn tại trong hệ thống"
                    });
                }

                // Kiểm tra mật khẩu (trong thực tế nên hash password)
                if (user.Password != loginRequest.Password)
                {
                    return Ok(new LoginResponse
                    {
                        Success = false,
                        Message = "Mật khẩu không chính xác"
                    });
                }

                // Đăng nhập thành công
                var userInfo = new UserInfo
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    Role = user.Role,
                    DateOfBirth = user.DateOfBirth,
                    Address = user.Address,
                    PhoneNumber = user.PhoneNumber,
                    CreatedAt = user.CreatedAt
                };

                return Ok(new LoginResponse
                {
                    Success = true,
                    Message = "Đăng nhập thành công",
                    User = userInfo
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new LoginResponse
                {
                    Success = false,
                    Message = $"Lỗi server: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Đăng ký người dùng mới
        /// </summary>
        /// <param name="user">Thông tin người dùng</param>
        /// <returns>Kết quả đăng ký</returns>
        [HttpPost("register")]
        public async Task<ActionResult<LoginResponse>> Register([FromBody] User user)
        {
            try
            {
                // Kiểm tra dữ liệu đầu vào
                if (!ModelState.IsValid)
                {
                    return BadRequest(new LoginResponse
                    {
                        Success = false,
                        Message = "Dữ liệu đầu vào không hợp lệ"
                    });
                }

                // Kiểm tra email đã tồn tại chưa
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == user.Email.ToLower());

                if (existingUser != null)
                {
                    return Ok(new LoginResponse
                    {
                        Success = false,
                        Message = "Email đã được sử dụng"
                    });
                }

                // Tạo user mới
                user.CreatedAt = DateTime.UtcNow;
                user.Role = user.Role ?? "User"; // Mặc định là User

                _context.Users.Add(user);
                await _context.SaveChangesAsync();

                // Trả về thông tin user (không bao gồm password)
                var userInfo = new UserInfo
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    Role = user.Role,
                    DateOfBirth = user.DateOfBirth,
                    Address = user.Address,
                    PhoneNumber = user.PhoneNumber,
                    CreatedAt = user.CreatedAt
                };

                return Ok(new LoginResponse
                {
                    Success = true,
                    Message = "Đăng ký thành công",
                    User = userInfo
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new LoginResponse
                {
                    Success = false,
                    Message = $"Lỗi server: {ex.Message}"
                });
            }
        }

        /// <summary>
        /// Kiểm tra email có tồn tại không
        /// </summary>
        /// <param name="email">Email cần kiểm tra</param>
        /// <returns>True nếu email tồn tại</returns>
        [HttpGet("check-email/{email}")]
        public async Task<ActionResult<bool>> CheckEmailExists(string email)
        {
            try
            {
                var exists = await _context.Users
                    .AnyAsync(u => u.Email.ToLower() == email.ToLower());
                
                return Ok(exists);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Lỗi server: {ex.Message}");
            }
        }
    }
}
